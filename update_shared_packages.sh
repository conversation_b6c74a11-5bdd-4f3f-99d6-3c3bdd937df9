#!/bin/bash

echo "Updating package declarations for shared components..."

# Update shared application components
find src/main/java/com/sportal365/articlescheduler/shared/application -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.application\.service;|package com.sportal365.articlescheduler.shared.application.service;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/shared/application -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.application\.service\.common;|package com.sportal365.articlescheduler.shared.application.service;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/shared/application -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.application\.dto\.common;|package com.sportal365.articlescheduler.shared.application.dto.common;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/shared/application -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.application\.dto\.common\.response;|package com.sportal365.articlescheduler.shared.application.dto.common.response;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/shared/application -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.application\.dto\.template;|package com.sportal365.articlescheduler.shared.application.dto.template;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/shared/application -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.application\.util;|package com.sportal365.articlescheduler.shared.application.util;|g' {} \;

# Update shared domain components
find src/main/java/com/sportal365/articlescheduler/shared/domain -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.domain\.model;|package com.sportal365.articlescheduler.shared.domain.model;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/shared/domain -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.domain\.model\.enums;|package com.sportal365.articlescheduler.shared.domain.model.enums;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/shared/domain -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.domain\.utils;|package com.sportal365.articlescheduler.shared.domain.utils;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/shared/domain -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.domain\.exception;|package com.sportal365.articlescheduler.shared.domain.exception;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/shared/domain -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.domain\.widget\.model;|package com.sportal365.articlescheduler.shared.domain.widget.model;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/shared/domain -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.domain\.widget\.service;|package com.sportal365.articlescheduler.shared.domain.widget.service;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/shared/domain -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.domain\.widget\.client;|package com.sportal365.articlescheduler.shared.domain.widget.client;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/shared/domain -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.domain\.widget\.client\.configuration;|package com.sportal365.articlescheduler.shared.domain.widget.client.configuration;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/shared/domain -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.domain\.calculator;|package com.sportal365.articlescheduler.shared.domain.calculator;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/shared/domain -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.domain\.repository;|package com.sportal365.articlescheduler.shared.domain.repository;|g' {} \;

# Update shared infrastructure components
find src/main/java/com/sportal365/articlescheduler/shared/infrastructure -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.infrastructure\.config;|package com.sportal365.articlescheduler.shared.infrastructure.config;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/shared/infrastructure -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.infrastructure\.constant;|package com.sportal365.articlescheduler.shared.infrastructure.constant;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/shared/infrastructure -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.infrastructure\.events;|package com.sportal365.articlescheduler.shared.infrastructure.events;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/shared/infrastructure -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.infrastructure\.client\.common;|package com.sportal365.articlescheduler.shared.infrastructure.client.common;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/shared/infrastructure -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.infrastructure\.client\.common\.domain;|package com.sportal365.articlescheduler.shared.infrastructure.client.common.domain;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/shared/infrastructure -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.infrastructure\.persistence\.migration;|package com.sportal365.articlescheduler.shared.infrastructure.persistence.migration;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/shared/infrastructure -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.infrastructure\.persistence\.repository;|package com.sportal365.articlescheduler.shared.infrastructure.persistence.repository;|g' {} \;

echo "Package declarations updated for shared components!"
