#!/bin/bash

echo "Updating import statements throughout the codebase..."

# Update imports for schedules domain
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.application\.service\.schedules\.|import com.sportal365.articlescheduler.schedules.application.service.|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.application\.dto\.schedule\.request\.|import com.sportal365.articlescheduler.schedules.application.dto.request.|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.application\.dto\.schedule\.response\.|import com.sportal365.articlescheduler.schedules.application.dto.response.|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.domain\.model\.Schedule;|import com.sportal365.articlescheduler.schedules.domain.model.Schedule;|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.domain\.model\.enums\.ScheduleStatus;|import com.sportal365.articlescheduler.schedules.domain.model.enums.ScheduleStatus;|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.domain\.model\.mappers\.ScheduleMapper;|import com.sportal365.articlescheduler.schedules.domain.model.mappers.ScheduleMapper;|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.domain\.repository\.ScheduleRepository;|import com.sportal365.articlescheduler.schedules.domain.repository.ScheduleRepository;|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.domain\.query\.ScheduleQueryBuilder;|import com.sportal365.articlescheduler.schedules.domain.query.ScheduleQueryBuilder;|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.domain\.validator\.ScheduleRequestValidator;|import com.sportal365.articlescheduler.schedules.domain.validator.ScheduleRequestValidator;|g' {} \;

# Update imports for article generation domain
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.application\.service\.article\.generation\.|import com.sportal365.articlescheduler.article.generation.application.service.|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.application\.service\.article\.content\.|import com.sportal365.articlescheduler.article.generation.application.service.|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.application\.service\.article\.templates\.|import com.sportal365.articlescheduler.article.generation.application.service.|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.application\.dto\.article\.|import com.sportal365.articlescheduler.article.generation.application.dto.|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.domain\.model\.ArticleExample;|import com.sportal365.articlescheduler.article.generation.domain.model.ArticleExample;|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.domain\.model\.Template;|import com.sportal365.articlescheduler.article.generation.domain.model.Template;|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.domain\.model\.ParagraphTemplate;|import com.sportal365.articlescheduler.article.generation.domain.model.ParagraphTemplate;|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.domain\.llm\.model\.dto\.|import com.sportal365.articlescheduler.article.generation.domain.model.llm.|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.domain\.llm\.enums\.ContentStatus;|import com.sportal365.articlescheduler.article.generation.domain.model.enums.ContentStatus;|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.domain\.template\.model\.|import com.sportal365.articlescheduler.article.generation.domain.model.template.|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.infrastructure\.client\.llm\.|import com.sportal365.articlescheduler.article.generation.infrastructure.client.|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.infrastructure\.client\.contentapi\.|import com.sportal365.articlescheduler.article.generation.infrastructure.client.|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.infrastructure\.persistence\.repository\.TemplateRepository;|import com.sportal365.articlescheduler.article.generation.infrastructure.persistence.repository.TemplateRepository;|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.infrastructure\.persistence\.repository\.ParagraphTemplateRepository;|import com.sportal365.articlescheduler.article.generation.infrastructure.persistence.repository.ParagraphTemplateRepository;|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.infrastructure\.persistence\.entity\.TemplateDocument;|import com.sportal365.articlescheduler.article.generation.infrastructure.persistence.entity.TemplateDocument;|g' {} \;

# Update imports for common services
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.application\.service\.debug\.|import com.sportal365.articlescheduler.application.service.common.|g' {} \;

echo "Import statements updated successfully!"
