#!/bin/bash

# Update schedules domain package declarations
find src/main/java/com/sportal365/articlescheduler/schedules -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.application\.dto\.schedule\.request;|package com.sportal365.articlescheduler.schedules.application.dto.request;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/schedules -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.application\.dto\.schedule\.response;|package com.sportal365.articlescheduler.schedules.application.dto.response;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/schedules -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.domain\.model;|package com.sportal365.articlescheduler.schedules.domain.model;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/schedules -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.domain\.model\.enums;|package com.sportal365.articlescheduler.schedules.domain.model.enums;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/schedules -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.domain\.model\.mappers;|package com.sportal365.articlescheduler.schedules.domain.model.mappers;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/schedules -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.domain\.repository;|package com.sportal365.articlescheduler.schedules.domain.repository;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/schedules -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.domain\.query;|package com.sportal365.articlescheduler.schedules.domain.query;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/schedules -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.domain\.validator;|package com.sportal365.articlescheduler.schedules.domain.validator;|g' {} \;

# Update article generation domain package declarations
find src/main/java/com/sportal365/articlescheduler/article/generation -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.application\.service\.article\.generation;|package com.sportal365.articlescheduler.article.generation.application.service;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/article/generation -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.application\.service\.article\.content;|package com.sportal365.articlescheduler.article.generation.application.service;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/article/generation -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.application\.service\.article\.templates;|package com.sportal365.articlescheduler.article.generation.application.service;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/article/generation -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.application\.dto\.article;|package com.sportal365.articlescheduler.article.generation.application.dto;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/article/generation -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.application\.dto\.article\.response;|package com.sportal365.articlescheduler.article.generation.application.dto.response;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/article/generation -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.domain\.model;|package com.sportal365.articlescheduler.article.generation.domain.model;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/article/generation -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.domain\.llm\.model\.dto;|package com.sportal365.articlescheduler.article.generation.domain.model.llm;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/article/generation -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.domain\.llm\.enums;|package com.sportal365.articlescheduler.article.generation.domain.model.enums;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/article/generation -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.domain\.template\.model;|package com.sportal365.articlescheduler.article.generation.domain.model.template;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/article/generation -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.infrastructure\.client\.llm;|package com.sportal365.articlescheduler.article.generation.infrastructure.client;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/article/generation -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.infrastructure\.client\.contentapi;|package com.sportal365.articlescheduler.article.generation.infrastructure.client;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/article/generation -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.infrastructure\.client\.contentapi\.configuration;|package com.sportal365.articlescheduler.article.generation.infrastructure.client;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/article/generation -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.infrastructure\.persistence\.repository;|package com.sportal365.articlescheduler.article.generation.infrastructure.persistence.repository;|g' {} \;
find src/main/java/com/sportal365/articlescheduler/article/generation -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.infrastructure\.persistence\.entity;|package com.sportal365.articlescheduler.article.generation.infrastructure.persistence.entity;|g' {} \;

# Update common service package declaration
find src/main/java/com/sportal365/articlescheduler/application/service/common -name "*.java" -type f -exec sed -i '' 's|package com\.sportal365\.articlescheduler\.application\.service\.debug;|package com.sportal365.articlescheduler.application.service.common;|g' {} \;

echo "Package declarations updated successfully!"
