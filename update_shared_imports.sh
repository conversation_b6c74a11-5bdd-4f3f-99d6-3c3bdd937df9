#!/bin/bash

echo "Updating import statements to reference shared components..."

# Update imports for shared application components
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.application\.service\.ProjectService;|import com.sportal365.articlescheduler.shared.application.service.ProjectService;|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.application\.service\.common\.|import com.sportal365.articlescheduler.shared.application.service.|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.application\.dto\.common\.|import com.sportal365.articlescheduler.shared.application.dto.common.|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.application\.dto\.template\.|import com.sportal365.articlescheduler.shared.application.dto.template.|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.application\.util\.|import com.sportal365.articlescheduler.shared.application.util.|g' {} \;

# Update imports for shared domain components
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.domain\.model\.MatchDetails;|import com.sportal365.articlescheduler.shared.domain.model.MatchDetails;|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.domain\.model\.Widget;|import com.sportal365.articlescheduler.shared.domain.model.Widget;|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.domain\.model\.Changelog;|import com.sportal365.articlescheduler.shared.domain.model.Changelog;|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.domain\.model\.enums\.|import com.sportal365.articlescheduler.shared.domain.model.enums.|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.domain\.utils\.|import com.sportal365.articlescheduler.shared.domain.utils.|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.domain\.exception\.|import com.sportal365.articlescheduler.shared.domain.exception.|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.domain\.widget\.|import com.sportal365.articlescheduler.shared.domain.widget.|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.domain\.calculator\.|import com.sportal365.articlescheduler.shared.domain.calculator.|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.domain\.repository\.ChangelogRepository;|import com.sportal365.articlescheduler.shared.domain.repository.ChangelogRepository;|g' {} \;

# Update imports for shared infrastructure components
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.infrastructure\.config\.|import com.sportal365.articlescheduler.shared.infrastructure.config.|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.infrastructure\.constant\.|import com.sportal365.articlescheduler.shared.infrastructure.constant.|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.infrastructure\.events\.|import com.sportal365.articlescheduler.shared.infrastructure.events.|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.infrastructure\.client\.common\.|import com.sportal365.articlescheduler.shared.infrastructure.client.common.|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.infrastructure\.persistence\.migration\.|import com.sportal365.articlescheduler.shared.infrastructure.persistence.migration.|g' {} \;
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.infrastructure\.persistence\.repository\.WidgetRepository;|import com.sportal365.articlescheduler.shared.infrastructure.persistence.repository.WidgetRepository;|g' {} \;

echo "Import statements updated for shared components!"
