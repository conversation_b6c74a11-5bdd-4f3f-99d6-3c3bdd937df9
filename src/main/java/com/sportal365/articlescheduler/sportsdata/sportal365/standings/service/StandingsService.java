package com.sportal365.articlescheduler.sportsdata.sportal365.standings.service;

import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.schedules.domain.model.Schedule;
import com.sportal365.articlescheduler.sportsdata.match.MatchDetailEnrichService;
import com.sportal365.articlescheduler.sportsdata.sportal365.standings.client.StandingsApiClient;
import com.sportal365.articlescheduler.sportsdata.sportal365.standings.client.model.StandingsResponse;
import com.sportal365.articlescheduler.sportsdata.sportal365.standings.mappers.StandingsMapper;
import com.sportal365.common.enums.SportEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.concurrent.atomic.AtomicBoolean;

@Service
@RequiredArgsConstructor
public class StandingsService implements MatchDetailEnrichService {

    private final StandingsApiClient standingsApiClient;

    private final StandingsMapper standingsMapper;

    public StandingsResponse getStandingsForParticipants(String seasonId, SportEnum sport, String stageId) {
        AtomicBoolean isPlayoffs = new AtomicBoolean(false);
        StandingsResponse response = standingsApiClient.getStandingsByStageId(sport.getSportValue(), stageId)
                .doOnNext(result -> isPlayoffs.set(false))
                .switchIfEmpty(Mono.defer(() -> {
                    isPlayoffs.set(true);
                    return standingsApiClient.getStandingsBySeasonId(sport.getSportValue(), seasonId);
                }))
                .block(Duration.ofSeconds(10));
        if (response != null) {
            response.setPlayoffs(isPlayoffs.get());
        }
        return response;
    }

    @Override
    public MatchDetails enrich(MatchDetails matchDetails, Schedule schedule) {
        StandingsResponse standingsForParticipants = getStandingsForParticipants(matchDetails.getSeasonId(), schedule.getSport(), matchDetails.getStageId());
        return matchDetails.toBuilder()
                .standings(standingsMapper.map(standingsForParticipants, matchDetails.getParticipantIds()))
                .build();
    }
}
