package com.sportal365.articlescheduler.sportsdata.sportal365.sportsearch.service;

import com.sportal365.articlescheduler.domain.exception.ResourceNotFoundException;
import com.sportal365.articlescheduler.domain.model.MatchDetails;
import com.sportal365.articlescheduler.schedules.domain.model.Schedule;
import com.sportal365.articlescheduler.sportsdata.match.MatchDetailEnrichService;
import com.sportal365.articlescheduler.sportsdata.sportal365.sportsearch.client.SportSearchClient;
import com.sportal365.articlescheduler.sportsdata.sportal365.sportsearch.client.model.SportSearchResponse;
import com.sportal365.articlescheduler.sportsdata.sportal365.sportsearch.mappers.SportSearchMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class SportSearchService implements MatchDetailEnrichService {

    private final SportSearchMapper mapper;

    private final SportSearchClient sportSearchClient;

    @Override
    public MatchDetails enrich(MatchDetails matchDetails, Schedule schedule) {
        return mapper.map(getSportSearchResponse(schedule.getMatchDetails().getMatchId(), schedule.getProjectDomain(), schedule.getLanguage()), schedule.getTimeZone());
    }

    public SportSearchResponse getSportSearchResponse(String matchUuid, String project, String translationLanguage) {

        try {

            SportSearchResponse sportSearchResponse = sportSearchClient.getEventById(matchUuid, project, translationLanguage).block();

            if (sportSearchResponse == null || sportSearchResponse.getResults() == null || sportSearchResponse.getResults().isEmpty()) {
                throw new ResourceNotFoundException("There is no event found for match ID: " + matchUuid);
            }

            return sportSearchResponse;
        } catch (Exception e) {
            log.error("Error fetching event by ID: {} with error {}", matchUuid, e.getMessage());
            throw new RuntimeException("Failed to fetch event with ID: " + matchUuid, e);
        }
    }
}
