package com.sportal365.articlescheduler.shared.application.service;

import com.sportal365.articlescheduler.article.generation.application.service.TemplateService;
import com.sportal365.articlescheduler.shared.infrastructure.events.DefaultTemplateCreatedEvent;
import com.sportal365.configurationclient.model.Project;
import com.sportal365.configurationclient.storage.InMemoryConfigurationStorage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProjectService {
    private final InMemoryConfigurationStorage inMemoryConfigurationStorage;
    private final TemplateService templateService;

    @EventListener
    public void handleDefaultTemplateCreated(DefaultTemplateCreatedEvent event) {
        log.info("Handling DefaultTemplateCreatedEvent");
        createDefaultTemplatesForAiEnabledProjects();
    }

    public void createDefaultTemplateProject(String projectName) {
        templateService.createDefaultTemplates(projectName);
    }

    /**
     * Finds all projects that have AI article generation service enabled.
     *
     * @return List of project names with AI enabled
     */
    public List<String> findAiEnabledProjects() {
        log.info("Finding AI enabled projects");

        Map<String, Project> projectMap = inMemoryConfigurationStorage.getProjectConfigurationMap();

        return projectMap.keySet().stream()
                .filter(this::isAiEnabled)
                .collect(Collectors.toList());
    }

    /**
     * Checks if a project has AI article generation service enabled.
     *
     * @param projectName The project name to check
     * @return true if AI is enabled, false otherwise
     */
    public boolean isAiEnabled(String projectName) {
        Project project = inMemoryConfigurationStorage.getProjectConfigurationMap().get(projectName);
        return project != null
                && project.getConfiguration() != null
                && project.getConfiguration().getServices() != null
                && project.getConfiguration().getServices().getAiArticleGenerationService() != null
                && project.getConfiguration().getServices().getAiArticleGenerationService().getEnabled();
    }

    public void createDefaultTemplatesForAiEnabledProjects() {
        findAiEnabledProjects().forEach(this::createDefaultTemplateProject);
    }

}
