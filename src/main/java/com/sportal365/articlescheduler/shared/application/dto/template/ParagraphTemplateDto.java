package com.sportal365.articlescheduler.shared.application.dto.template;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ParagraphTemplateDto {
    private String name;

    private String version;

    private String category;

    private String description;

    @JsonProperty("text_paragraph")
    private String textParagraph;

    private String sport;

    private String status;
}
