package com.sportal365.articlescheduler.application.dto.article.response;

import com.fasterxml.jackson.annotation.JsonProperty;

public record ArticleResponseDto(@JsonProperty("template_type") String templateType,
                                 @JsonProperty("schedule_id") String scheduleId,
                                 @JsonProperty("posted_article_id") String articleId,
                                 @JsonProperty("usage") Usage usage) {

    public record Usage(@JsonProperty("generation_tokens") int generationTokens,
                        @JsonProperty("prompt_tokens") int promptTokens) {

    }
}
