package com.sportal365.articlescheduler.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

@Document(collection = "paragraphs")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ParagraphTemplate {
    @Id
    private String id;
    private String name;
    private String version;
    private String category;
    private String description;
    @Field("text_paragraph")
    @JsonProperty("text_paragraph")
    private String textParagraph;
    @JsonProperty("created_date")
    private Date createdDate;
    private String sport;
    private String status;
    @Field("project_domain")
    private String projectDomain;
}
