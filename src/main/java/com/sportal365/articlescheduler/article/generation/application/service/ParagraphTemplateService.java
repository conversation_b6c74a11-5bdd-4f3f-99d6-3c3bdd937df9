package com.sportal365.articlescheduler.application.service.article.templates;

import com.sportal365.articlescheduler.application.dto.template.ParagraphTemplateDto;
import com.sportal365.articlescheduler.domain.exception.DuplicateTemplateException;
import com.sportal365.articlescheduler.domain.exception.TemplateNotFoundException;
import com.sportal365.articlescheduler.domain.model.ParagraphTemplate;
import com.sportal365.articlescheduler.infrastructure.persistence.repository.ParagraphTemplateRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
@RequiredArgsConstructor
public class ParagraphTemplateService {

    private final ParagraphTemplateRepository repository;

    public ParagraphTemplate create(ParagraphTemplateDto dto, String projectDomain) {

        if (repository.findByName(dto.getName()).isPresent()) {
            throw new DuplicateTemplateException("Template with name " + dto.getName() + " already exists");
        }

        ParagraphTemplate template = mapToEntity(dto, projectDomain);
        return repository.save(template);
    }

    public ParagraphTemplate update(String id, ParagraphTemplateDto templateDto) {

        ParagraphTemplate existingTemplate = repository.findById(id)
                .orElseThrow(() -> new TemplateNotFoundException("Template with id " + id + " not found"));

        if (!existingTemplate.getName().equals(templateDto.getName()) &&
                repository.findByName(templateDto.getName()).isPresent()) {
            throw new DuplicateTemplateException("Template with name " + templateDto.getName() + " already exists");
        }

        updateEntityFromDto(templateDto, existingTemplate);
        return repository.save(existingTemplate);
    }

    public void delete(String id) {

        if (!repository.existsById(id)) {
            throw new TemplateNotFoundException("Template with id " + id + " not found");
        }
        repository.deleteById(id);
    }

    public ParagraphTemplate getTemplate(String templateId) {

        return repository.findById(templateId)
                .orElseThrow(() -> new TemplateNotFoundException("Template with id " + templateId + " not found"));

    }

    public List<ParagraphTemplate> listTemplates() {

        return repository.findAll();
    }

    private ParagraphTemplate mapToEntity(ParagraphTemplateDto dto, String projectDomain) {
        return ParagraphTemplate.builder()
                .name(dto.getName())
                .version(dto.getVersion())
                .category(dto.getCategory())
                .description(dto.getDescription())
                .textParagraph(dto.getTextParagraph())
                .sport(dto.getSport())
                .status(dto.getStatus())
                .createdDate(new Date())
                .projectDomain(projectDomain)
                .build();
    }

    private void updateEntityFromDto(ParagraphTemplateDto dto, ParagraphTemplate entity) {
        entity.setName(dto.getName());
        entity.setVersion(dto.getVersion());
        entity.setCategory(dto.getCategory());
        entity.setDescription(dto.getDescription());
        entity.setTextParagraph(dto.getTextParagraph());
        entity.setSport(dto.getSport());
        entity.setStatus(dto.getStatus());
    }
}
