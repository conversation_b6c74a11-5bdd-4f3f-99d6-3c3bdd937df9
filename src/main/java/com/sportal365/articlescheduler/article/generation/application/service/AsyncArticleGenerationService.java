package com.sportal365.articlescheduler.article.generation.application.service;

import com.sportal365.articlescheduler.article.generation.application.dto.response.ArticleResponseDto;
import com.sportal365.articlescheduler.domain.calculator.ArticleCalculator;
import com.sportal365.articlescheduler.domain.model.Changelog;
import com.sportal365.articlescheduler.schedules.domain.model.Schedule;
import com.sportal365.articlescheduler.schedules.domain.model.enums.ScheduleStatus;
import com.sportal365.articlescheduler.domain.repository.ChangelogRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class AsyncArticleGenerationService {

    private final ArticleGenerationService articleGenerationService;
    private final ChangelogRepository changelogRepository;

    private static final int MAX_ATTEMPTS = 3;

    private final Executor taskExecutor;

    /**
     * Processes schedules asynchronously by generating articles.
     * @param schedules the list of schedules to process
     * @return the list of updated schedules after processing
     */
    public List<Schedule> processSchedules(List<Schedule> schedules) {
        List<CompletableFuture<Schedule>> futures = generateArticlesAsync(schedules);
        // Wait for all tasks to complete.
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        // Return the updated schedules.
        return futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());
    }

    /**
     * Processes a list of schedules asynchronously. For each schedule, it calls the article generation service,
     * calculates the price, updates its status, and returns the updated schedule.
     *
     * @param schedules the list of schedules to process
     * @return a list of CompletableFuture that will complete with the updated schedules
     */
    public List<CompletableFuture<Schedule>> generateArticlesAsync(List<Schedule> schedules) {
        List<Changelog> changelogs = Collections.synchronizedList(new ArrayList<>());

        List<CompletableFuture<Schedule>> futures = schedules.stream()
                .map(schedule -> CompletableFuture.supplyAsync(() -> {
                    String oldStatus = schedule.getStatus().name();
                    try {
                        ArticleResponseDto responseDto = articleGenerationService.generateArticle(schedule);

                        // Update the schedule with response details
                        schedule.setPriceForGeneratedArticle(ArticleCalculator.calculateArticlePrice(responseDto.usage()));
                        schedule.setArticleId(responseDto.articleId());
                        schedule.setStatus(ScheduleStatus.COMPLETED);

                        // Create changelog for successful completion
                        changelogs.add(createChangelog(schedule, oldStatus, ScheduleStatus.COMPLETED.name()));

                        log.info("Successfully generated article for schedule {}", schedule.getId());
                    } catch (Exception e) {

                        // Update retry attempts and adjust status accordingly
                        int retriedAttempts = schedule.getRetriedAttempts() + 1;
                        ScheduleStatus newStatus = ScheduleStatus.RETRY;

                        if (retriedAttempts <= AsyncArticleGenerationService.MAX_ATTEMPTS) {
                            schedule.setStatus(newStatus);
                            schedule.setRetriedAttempts(retriedAttempts);
                        } else {
                            newStatus = ScheduleStatus.FAILED;
                            schedule.setStatus(newStatus);
                        }

                        if (!oldStatus.equals(newStatus.name())) {
                            changelogs.add(createChangelog(schedule, oldStatus, newStatus.name()));
                        }

                        log.error("Failed to generate article for schedule {}: {}", schedule.getId(), e.getMessage(), e);
                    }
                    return schedule;
                }, taskExecutor))
                .collect(Collectors.toList());

        // Wait for all futures to complete and then save changelogs in batch
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenRun(() -> {
                    if (!changelogs.isEmpty()) {
                        changelogRepository.saveAll(changelogs);
                        log.info("Saved {} changelogs in batch", changelogs.size());
                    }
                });

        return futures;
    }

    /**
     * Creates a changelog entry to track status changes for a schedule.
     * This method captures the state transition of a schedule during the article generation process,
     * recording both the old and new status along with relevant metadata for audit purposes.
     *
     * The changelog is used for:
     * - Tracking the progression of schedule processing
     * - Auditing status changes for debugging and monitoring
     * - Providing historical data for analytics and reporting
     *
     * @param schedule the schedule that underwent a status change
     * @param oldStatus the previous status of the schedule before the change
     * @param newStatus the new status of the schedule after the change
     * @return Changelog object containing the status change details with timestamp
     */
    private Changelog createChangelog(Schedule schedule, String oldStatus, String newStatus) {
        Changelog changelog = Changelog.builder()
                .scheduleId(schedule.getId())
                .matchName(schedule.getMatchDetails().getMatchName())
                .statusNew(newStatus)
                .statusOld(oldStatus)
                .updatedAt(Instant.now())
                .build();

        log.debug("Created changelog for schedule {} status change from {} to {}",
                schedule.getId(), oldStatus, newStatus);

        return changelog;
    }
}
