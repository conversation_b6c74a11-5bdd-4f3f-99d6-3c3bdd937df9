package com.sportal365.articlescheduler.article.generation.application.service;

import com.sportal365.articlescheduler.application.dto.template.WidgetDto;
import com.sportal365.articlescheduler.domain.exception.DuplicateWidgetException;
import com.sportal365.articlescheduler.domain.exception.WidgetNotFoundException;
import com.sportal365.articlescheduler.domain.model.Widget;
import com.sportal365.articlescheduler.domain.model.enums.WidgetTypeEnum;
import com.sportal365.articlescheduler.infrastructure.persistence.repository.WidgetRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
@RequiredArgsConstructor
public class WidgetService {

    private final WidgetRepository repository;

    public Widget create(WidgetDto dto) {
        if (repository.findByName(dto.getName()).isPresent()) {
            throw new DuplicateWidgetException("Widget with name " + dto.getName() + " already exists");
        }

        Widget widget = mapToEntity(dto);
        return repository.save(widget);
    }

    public Widget update(String id, WidgetDto dto) {
        Widget existingWidget = repository.findById(id)
                .orElseThrow(() -> new WidgetNotFoundException("Widget with id " + id + " not found"));

        if (!existingWidget.getName().equals(dto.getName()) &&
                repository.findByName(dto.getName()).isPresent()) {
            throw new DuplicateWidgetException("Widget with name " + dto.getName() + " already exists");
        }

        updateEntityFromDto(dto, existingWidget);
        return repository.save(existingWidget);
    }

    public boolean delete(String id) {
        if (!repository.existsById(id)) {
            throw new WidgetNotFoundException("Widget with id " + id + " not found");
        }

        repository.deleteById(id);
        return true;
    }

    public Widget getWidget(String id) {
        return repository.findById(id)
                .orElseThrow(() -> new WidgetNotFoundException("Widget with id " + id + " not found"));
    }

    public List<Widget> listWidgets() {
        return repository.findAll();
    }

    private Widget mapToEntity(WidgetDto dto) {
        return Widget.builder()
                .name(dto.getName())
                .version(dto.getVersion())
                .widgetType(WidgetTypeEnum.of(dto.getType()))
                .description(dto.getDescription())
                .status(dto.getStatus())
                .createdDate(new Date())
                .build();
    }

    private void updateEntityFromDto(WidgetDto dto, Widget entity) {
        entity.setName(dto.getName());
        entity.setWidgetType(WidgetTypeEnum.of(dto.getType()));
        entity.setVersion(dto.getVersion());
        entity.setDescription(dto.getDescription());
        entity.setStatus(dto.getStatus());
    }
}
