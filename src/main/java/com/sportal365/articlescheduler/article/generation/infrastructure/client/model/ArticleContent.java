package com.sportal365.articlescheduler.article.generation.infrastructure.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sportal365.articlescheduler.domain.widget.model.WidgetResponse;
import lombok.Builder;
import lombok.Value;

import java.util.List;
import java.util.Map;

@Value
@Builder
public class ArticleContent {
    List<ArticleBlock> body;

    @Value
    @Builder
    public static class ArticleBlock {
        String id;
        String type;
        BlockData data;
    }

    @Value
    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class BlockData {
        String content;
        String placeholderName;
        String type;
        String sport;
        Map<String, Object> preview;
        String changeId;
        WidgetResponse.WidgetConfig config;
        @JsonProperty("widget_type")
        String widgetType;
    }
}