package com.sportal365.articlescheduler.domain.model;

import com.sportal365.articlescheduler.domain.llm.model.dto.ArticleStructure;
import com.sportal365.articlescheduler.domain.template.model.PromptTemplate;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.Instant;

@Document(collection = "article_examples")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ArticleExample {

    @Id
    private String id;

    private ArticleStructure article;

    private Instant currentDate;

    private String eventName;

    private String eventDate;

    private PromptTemplate.Parameters parameters;
}
