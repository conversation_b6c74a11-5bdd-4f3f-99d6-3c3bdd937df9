package com.sportal365.articlescheduler.article.generation.application.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sportal365.articlescheduler.shared.domain.exception.TemplateNotFoundException;
import com.sportal365.articlescheduler.shared.domain.model.MatchDetails;
import com.sportal365.articlescheduler.article.generation.domain.model.ParagraphTemplate;
import com.sportal365.articlescheduler.schedules.domain.model.Schedule;
import com.sportal365.articlescheduler.shared.domain.model.enums.SectionType;
import com.sportal365.articlescheduler.shared.domain.model.enums.TemplateTypeEnum;
import com.sportal365.articlescheduler.article.generation.domain.model.template.PromptTemplate;
import com.sportal365.articlescheduler.article.generation.infrastructure.persistence.entity.TemplateDocument;
import com.sportal365.articlescheduler.article.generation.infrastructure.persistence.entity.TemplateId;
import com.sportal365.articlescheduler.article.generation.infrastructure.persistence.repository.ParagraphTemplateRepository;
import com.sportal365.articlescheduler.article.generation.infrastructure.persistence.repository.TemplateRepository;
import com.sportal365.articlescheduler.shared.application.service.SaveTemplateToMongoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Log4j2
public class TemplateGenerationService {

    private final TemplateRepository templateDocumentRepository;

    private final SaveTemplateToMongoService saveTemplateToMongoService;

    private final ParagraphTemplateRepository paragraphRepository;

    public PromptTemplate generateTemplate(MatchDetails matchDetails, String templateName, String language, Schedule.ProviderProperties providerProperties,
                                           TemplateTypeEnum templateType, String projectDomain)
            throws JsonProcessingException {

        TemplateId templateId = TemplateId.builder()
                .templateName(templateName)
                .templateType(templateType)
                .projectDomain(projectDomain)
                .build();

        log.info("Generate match template");

        Optional<TemplateDocument> templateDocument = templateDocumentRepository.findById(templateId);

        if (templateDocument.isPresent()) {
            templateDocument.get().getPromptTemplate().getParameters().getOutput().setLanguage(language);
            templateDocument.get().getPromptTemplate().getParameters().setMatchDetails(convertMatchDetailsToMap(matchDetails));

            PromptTemplate promptTemplate =
                    convertTemplateDocumentToPromptTemplate(templateDocument.get());
            promptTemplate.setProviderProperties(new PromptTemplate.ProviderProperties(providerProperties.getProvider()
                    , providerProperties.getLlmModel()));
            saveTemplateToMongoService.save(templateDocument.get(), matchDetails);
            return promptTemplate;

        } else {
            log.info("Template with templateName '{}' not found", templateId);
            throw new TemplateNotFoundException("Template with templateName '" + templateId + "' not found");
        }

    }

    private List<String> getParagraphs(List<TemplateDocument.Section> sections, List<PromptTemplate.IndexedSection> indexedSections) {

        List<String> result = new ArrayList<>();
        log.info("Get sections");

        for (TemplateDocument.Section section : sections) {
            if (section.getType().equals(SectionType.TEXT_PARAGRAPH.name())) {
                ParagraphTemplate existingTemplate = paragraphRepository.findById(section.getSectionId())
                        .orElseThrow(() -> new TemplateNotFoundException("Template with id " + section.getSectionId() + " not found"));
                indexedSections.add(PromptTemplate.IndexedSection.builder()
                        .section(section)
                        .paragraphIndex(result.size())
                        .build());
                result.add(existingTemplate.getTextParagraph());
            } else if (SectionType.WIDGET.name().equals(section.getType())) {
                indexedSections.add(PromptTemplate.IndexedSection.builder()
                        .section(section)
                        .build());
            }
        }
        return result;
    }

    private Map<String, Object> convertMatchDetailsToMap(MatchDetails matchDetails) {
        ObjectMapper mapper = new ObjectMapper();
        //create a JSON-like Map from the Java fields in MatchDetails
        return mapper.convertValue(matchDetails, new TypeReference<Map<String, Object>>() {});
    }

    private PromptTemplate convertTemplateDocumentToPromptTemplate(TemplateDocument templateDocument) {

        // Get the nested prompt template from the document
        TemplateDocument.PromptTemplate docPrompt = templateDocument.getPromptTemplate();

        // Create the target PromptTemplate instance (from the separate domain model)
        PromptTemplate promptTemplate = new PromptTemplate();

        promptTemplate.setPrompt(docPrompt.getPrompt());

        // Prepare parameters conversion
        PromptTemplate.Parameters parameters = new PromptTemplate.Parameters();
        TemplateDocument.Parameters docParameters = docPrompt.getParameters();

        // Map Structure
        if (docParameters.getStructure() != null) {
            PromptTemplate.Structure structure = new PromptTemplate.Structure();
            structure.setTitle(docParameters.getStructure().getTitle());
            List<PromptTemplate.IndexedSection> indexedSections = new ArrayList<>();
            structure.setSections(getParagraphs(docParameters.getStructure().getSections(), indexedSections));
            parameters.setStructure(structure);
            promptTemplate.setIndexedSections(indexedSections);
        }

        // Map KeyConsiderations
        if (docParameters.getKeyConsiderations() != null) {
            PromptTemplate.KeyConsiderations keyConsiderations = new PromptTemplate.KeyConsiderations();
            keyConsiderations.setDirectOutput(docParameters.getKeyConsiderations().getDirectOutput());
            keyConsiderations.setJournalisticTone(docParameters.getKeyConsiderations().getJournalisticTone());
            keyConsiderations.setTerminology(docParameters.getKeyConsiderations().getTerminology());
            keyConsiderations.setLogicalFlow(docParameters.getKeyConsiderations().getLogicalFlow());
            parameters.setKeyConsiderations(keyConsiderations);
        }

        // Copy exclusions and matchDetails directly
        parameters.setExclusions(docParameters.getExclusions());
        parameters.setMatchDetails(docParameters.getMatchDetails());

        // Map Output
        if (docParameters.getOutput() != null) {
            PromptTemplate.Output output = new PromptTemplate.Output();
            output.setFormat(docParameters.getOutput().getFormat());
            output.setLanguage(docParameters.getOutput().getLanguage());
            parameters.setOutput(output);
        }

        // Set the converted parameters into the prompt template and return
        promptTemplate.setParameters(parameters);

        return promptTemplate;
    }
}

