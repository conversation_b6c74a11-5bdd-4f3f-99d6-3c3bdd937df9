package com.sportal365.articlescheduler.presentation.controller;

import com.sportal365.articlescheduler.application.dto.template.ParagraphTemplateDto;
import com.sportal365.articlescheduler.article.generation.application.service.ParagraphTemplateService;
import com.sportal365.articlescheduler.article.generation.domain.model.ParagraphTemplate;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.net.URI;
import java.util.List;

import static com.sportal365.articlescheduler.presentation.constant.Constants.X_PROJECT;

@RestController
@RequestMapping("/paragraph-templates")
@RequiredArgsConstructor
@Tag(name = "Paragraph Template Management", description = "API endpoints for managing paragraph-based article templates")

public class ParagraphTemplateController {

    private final ParagraphTemplateService paragraphTemplateService;

    @Operation(summary = "Create a new paragraph template",
            description = "Creates a new template containing multiple ordered paragraphs")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201",
                    description = "Template created successfully",
                    content = @Content(schema = @Schema(implementation = ParagraphTemplate.class))),
            @ApiResponse(responseCode = "400",
                    description = "Invalid input data"),
            @ApiResponse(responseCode = "409",
                    description = "Template with the same name already exists")
    })
    @PostMapping
    public ResponseEntity<ParagraphTemplate> create(
            @Parameter(description = "Project domain for multi-tenancy")
            @RequestHeader(X_PROJECT) String projectDomain,
            @Parameter(description = "Paragraph template data", required = true)
            @RequestBody ParagraphTemplateDto templateDto) {
        ParagraphTemplate created = paragraphTemplateService.create(templateDto, projectDomain);
        return ResponseEntity
                .created(URI.create("/paragraph-templates/" + created.getId()))
                .body(created);
    }

    @Operation(summary = "Get a specific paragraph template",
            description = "Retrieves a paragraph template by its ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200",
                    description = "Template found",
                    content = @Content(schema = @Schema(implementation = ParagraphTemplate.class))),
            @ApiResponse(responseCode = "404",
                    description = "Template not found")
    })
    @GetMapping("/{id}")
    public ResponseEntity<ParagraphTemplate> getTemplate(
            @Parameter(description = "Project domain for multi-tenancy", required = true)
            @RequestHeader(X_PROJECT) String projectDomain,
            @Parameter(description = "Template identifier", required = true)
            @PathVariable String id) {
        return ResponseEntity.ok(paragraphTemplateService.getTemplate(id));
    }

    @Operation(summary = "List paragraph templates",
            description = "Retrieves a list of paragraph templates for the specified project")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200",
                    description = "Templates retrieved successfully",
                    content = @Content(schema = @Schema(implementation = ParagraphTemplate.class)))
    })
    @GetMapping
    public ResponseEntity<List<ParagraphTemplate>> listTemplates(
            @Parameter(description = "Project domain for multi-tenancy", required = true)
            @RequestHeader(X_PROJECT) String projectDomain) {
        return ResponseEntity.ok(
                paragraphTemplateService.listTemplates()
        );
    }

    @Operation(summary = "Update paragraph template",
            description = "Performs a full update of an existing paragraph template")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200",
                    description = "Template updated successfully",
                    content = @Content(schema = @Schema(implementation = ParagraphTemplate.class))),
            @ApiResponse(responseCode = "404",
                    description = "Template not found"),
            @ApiResponse(responseCode = "400",
                    description = "Invalid input data")
    })
    @PutMapping("/{id}")
    public ResponseEntity<ParagraphTemplate> updateTemplate(
            @Parameter(description = "Project domain for multi-tenancy", required = true)
            @RequestHeader(X_PROJECT) String projectDomain,
            @Parameter(description = "Template identifier", required = true)
            @PathVariable String id,
            @Parameter(description = "Updated template data", required = true)
            @RequestBody ParagraphTemplateDto templateDto) {
        return ResponseEntity.ok(
                paragraphTemplateService.update(id, templateDto)
        );
    }

    @Operation(summary = "Delete paragraph template",
            description = "Deletes an existing paragraph template")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204",
                    description = "Template deleted successfully"),
            @ApiResponse(responseCode = "404",
                    description = "Template not found")
    })
    @DeleteMapping("/{id}")
    public ResponseEntity<Boolean> deleteTemplate(
            @Parameter(description = "Project domain for multi-tenancy", required = true)
            @RequestHeader(X_PROJECT) String projectDomain,
            @Parameter(description = "Template identifier", required = true)
            @PathVariable String id) {
        paragraphTemplateService.delete(id);
        return ResponseEntity.ok(true);
    }
}
