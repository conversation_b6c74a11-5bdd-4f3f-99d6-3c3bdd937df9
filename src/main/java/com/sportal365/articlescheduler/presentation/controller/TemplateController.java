package com.sportal365.articlescheduler.presentation.controller;

import com.sportal365.articlescheduler.application.dto.template.TemplateDto;
import com.sportal365.articlescheduler.article.generation.application.service.TemplateService;
import com.sportal365.articlescheduler.article.generation.domain.model.Template;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.net.URI;
import java.util.List;

import static com.sportal365.articlescheduler.presentation.constant.Constants.X_PROJECT;

@RestController
@RequestMapping("/templates")
@RequiredArgsConstructor
@Tag(name = "Template Management", description = "API endpoints for managing article templates")
public class TemplateController {
    private final TemplateService templateService;

    @Operation(summary = "Create a new template",
            description = "Creates a new article template for the specified project domain")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201",
                    description = "Template created successfully",
                    content = @Content(schema = @Schema(implementation = Template.class))),
            @ApiResponse(responseCode = "400",
                    description = "Invalid input data"),
            @ApiResponse(responseCode = "409",
                    description = "Template already exists")
    })
    @PostMapping
    public ResponseEntity<Template> createTemplate(
            @Parameter(description = "Project domain for multi-tenancy")
            @RequestHeader(X_PROJECT) String projectDomain,
            @Parameter(description = "Template data", required = true)
            @RequestBody TemplateDto templateDto) {
        Template created = templateService.createTemplate(templateDto, projectDomain);
        return ResponseEntity
                .created(URI.create("/templates/" + created.getTemplateType()))
                .body(created);
    }

    @Operation(summary = "Get a specific template",
            description = "Retrieves a template by project domain and article type")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200",
                    description = "Template found",
                    content = @Content(schema = @Schema(implementation = Template.class))),
            @ApiResponse(responseCode = "404",
                    description = "Template not found")
    })
    @GetMapping("/{articleType}/{templateName}")
    public ResponseEntity<Template> getTemplate(
            @Parameter(description = "Project domain for multi-tenancy", required = true)
            @RequestHeader(X_PROJECT) String projectDomain,
            @Parameter(description = "Article type identifier", required = true)
            @PathVariable String articleType,
            @Parameter(description = "Article name identifier", required = true)
            @PathVariable String templateName) {
        validateArticleType(articleType);
        return ResponseEntity.ok(templateService.getTemplate(projectDomain, articleType, templateName));
    }

    @Operation(summary = "List templates",
            description = "Retrieves a paginated list of templates with optional filters")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200",
                    description = "Templates retrieved successfully",
                    content = @Content(schema = @Schema(implementation = Template.class)))
    })
    @GetMapping
    public ResponseEntity<List<Template>> listTemplates(
            @Parameter(description = "Project domain for multi-tenancy", required = true)
            @RequestHeader(X_PROJECT) String projectDomain) {
        return ResponseEntity.ok(
                templateService.listTemplates(projectDomain)
        );
    }

    @Operation(summary = "Update template",
            description = "Performs a full update of an existing template")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200",
                    description = "Template updated successfully",
                    content = @Content(schema = @Schema(implementation = Template.class))),
            @ApiResponse(responseCode = "404",
                    description = "Template not found"),
            @ApiResponse(responseCode = "400",
                    description = "Invalid input data")
    })
    @PutMapping("/{templateType}/{templateName}")
    public ResponseEntity<Template> updateTemplate(
            @Parameter(description = "Project domain for multi-tenancy", required = true)
            @RequestHeader(X_PROJECT) String projectDomain,
            @Parameter(description = "Template type identifier", required = true)
            @PathVariable String templateType,
            @Parameter(description = "Template name identifier", required = true)
            @PathVariable String templateName,
            @Parameter(description = "Updated template data", required = true)
            @RequestBody TemplateDto templateDto) {
        validateArticleType(templateType);
        templateDto.setTemplateType(templateType);
        return ResponseEntity.ok(
                templateService.updateTemplate(templateDto, projectDomain, templateName, templateType)
        );
    }

    @Operation(summary = "Delete template",
            description = "Deletes an existing template")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204",
                    description = "Template deleted successfully"),
            @ApiResponse(responseCode = "404",
                    description = "Template not found")
    })
    @DeleteMapping("/{templateType}/{templateName}")
    public ResponseEntity<Void> deleteTemplate(
            @Parameter(description = "Project domain for multi-tenancy", required = true)
            @RequestHeader(X_PROJECT) String projectDomain,
            @Parameter(description = "Article type identifier", required = true)
            @PathVariable String templateType,
            @Parameter(description = "Article name identifier", required = true)
            @PathVariable String templateName) {
        validateArticleType(templateType);
        templateService.deleteTemplate(templateType, projectDomain, templateName);
        return ResponseEntity.noContent().build();
    }

    private void validateArticleType(String articleType) {
        if (articleType == null || articleType.isBlank()) {
            throw new IllegalArgumentException("Article type cannot be empty");
        }
    }
}
