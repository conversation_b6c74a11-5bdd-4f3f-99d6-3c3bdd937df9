package com.sportal365.articlescheduler.presentation.controller;

import com.sportal365.articlescheduler.shared.application.dto.template.WidgetDto;
import com.sportal365.articlescheduler.article.generation.application.service.WidgetService;
import com.sportal365.articlescheduler.shared.domain.model.Widget;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.net.URI;
import java.util.List;

@RestController
@RequestMapping("/widgets")
@RequiredArgsConstructor
@Tag(name = "Widget Management", description = "API endpoints for managing widgets")
public class WidgetController {

    private final WidgetService widgetService;

    @Operation(summary = "Create a new widget")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201",
                    description = "Widget created successfully",
                    content = @Content(schema = @Schema(implementation = Widget.class))),
            @ApiResponse(responseCode = "400",
                    description = "Invalid input data"),
            @ApiResponse(responseCode = "409",
                    description = "Widget with the same name already exists")
    })
    @PostMapping
    public ResponseEntity<Widget> create(
            @Parameter(description = "Widget data", required = true)
            @RequestBody WidgetDto widgetDto) {
        Widget created = widgetService.create(widgetDto);
        return ResponseEntity
                .created(URI.create("/widgets/" + created.getId()))
                .body(created);
    }

    @Operation(summary = "Get a specific widget")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200",
                    description = "Widget found",
                    content = @Content(schema = @Schema(implementation = Widget.class))),
            @ApiResponse(responseCode = "404",
                    description = "Widget not found")
    })
    @GetMapping("/{id}")
    public ResponseEntity<Widget> getWidget(
            @Parameter(description = "Widget identifier", required = true)
            @PathVariable String id) {
        return ResponseEntity.ok(widgetService.getWidget(id));
    }

    @Operation(summary = "List all widgets")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200",
                    description = "Widgets retrieved successfully",
                    content = @Content(schema = @Schema(implementation = Widget.class)))
    })
    @GetMapping
    public ResponseEntity<List<Widget>> listWidgets() {
        return ResponseEntity.ok(widgetService.listWidgets());
    }

    @Operation(summary = "Update widget")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200",
                    description = "Widget updated successfully",
                    content = @Content(schema = @Schema(implementation = Widget.class))),
            @ApiResponse(responseCode = "404",
                    description = "Widget not found"),
            @ApiResponse(responseCode = "400",
                    description = "Invalid input data")
    })
    @PutMapping("/{id}")
    public ResponseEntity<Widget> updateWidget(
            @Parameter(description = "Widget identifier", required = true)
            @PathVariable String id,
            @Parameter(description = "Updated widget data", required = true)
            @RequestBody WidgetDto widgetDto) {
        return ResponseEntity.ok(widgetService.update(id, widgetDto));
    }

    @Operation(summary = "Delete widget")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200",
                    description = "Widget deleted successfully",
                    content = @Content(schema = @Schema(implementation = Boolean.class))),
            @ApiResponse(responseCode = "404",
                    description = "Widget not found")
    })
    @DeleteMapping("/{id}")
    public ResponseEntity<Boolean> deleteWidget(
            @Parameter(description = "Widget identifier", required = true)
            @PathVariable String id) {
        return ResponseEntity.ok(widgetService.delete(id));
    }
}
