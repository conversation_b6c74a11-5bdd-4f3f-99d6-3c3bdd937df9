package com.sportal365.articlescheduler.domain.validator;

import com.sportal365.articlescheduler.application.dto.schedule.request.ScheduleCreateRequest;
import com.sportal365.articlescheduler.application.dto.schedule.request.ScheduleListRequest;
import com.sportal365.articlescheduler.application.dto.schedule.request.ScheduleMatchRequest;
import com.sportal365.articlescheduler.application.dto.common.Sport;
import com.sportal365.articlescheduler.domain.model.enums.ScheduleType;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

@Component
public class ScheduleRequestValidator {

    public void validate(ScheduleCreateRequest request) {
        List<String> errors = new ArrayList<>();

        // Check required fields in main request
        if (isNullOrBlank(request.getTemplateName())) {
            errors.add("template_name is required");
        }
        if (isNullOrBlank(request.getTemplateType())) {
            errors.add("template_type is required");
        }
        if (isNullOrBlank(request.getCategory())) {
            errors.add("category_id is required");
        } else {
            try {
                Long.parseLong(request.getCategory());
            } catch (NumberFormatException e) {
                errors.add("category_id must be a number");
            }
        }

        if (request.getSport() != null) {
            try {
                Sport.valueOf(request.getSport());
            } catch (IllegalArgumentException e) {
                errors.add("sport must be one of " + Arrays.toString(Sport.values()));
            }
        }

        if (isNullOrBlank(request.getCategoryName())) {
            errors.add("category_name is required");
        }

        if (request.getMatches() == null || request.getMatches().isEmpty()) {
            errors.add("matches array is required and cannot be empty");
        } else {
            for (int i = 0; i < request.getMatches().size(); i++) {
                ScheduleMatchRequest match = request.getMatches().get(i);
                validateMatch(match, i, errors);
            }
        }

        if (isNullOrBlank(request.getUserId())) {
            errors.add("user_id is required");
        } else {
            try {
                Long.parseLong(request.getUserId());
            } catch (NumberFormatException e) {
                errors.add("user_id must be a number");
            }
        }

        // Validate schedule_type if provided
        if (request.getScheduleType() != null) {
            if (request.getScheduleType() != ScheduleType.IMMEDIATELY &&
                request.getScheduleType() != ScheduleType.SCHEDULED) {
                errors.add("schedule_type must be 'immediately' or 'scheduled' when provided");
            }
        }

        if (!errors.isEmpty()) {
            throw new IllegalArgumentException("Validation failed: " + String.join(", ", errors));
        }
    }

    public void validateListRequest(ScheduleListRequest request) {
        if ((request.getFrom() != null && request.getTo() == null) || (request.getTo() != null && request.getFrom() == null)) {
            throw new IllegalArgumentException("Both from date and to date must be provided together");
        }
    }

    private void validateMatch(ScheduleMatchRequest match, int index, List<String> errors) {
        if (isNullOrBlank(match.getMatchId())) {
            errors.add(String.format("match[%d].match_id is required", index));
        } else {
            try {
                UUID uuid = UUID.fromString(match.getMatchId());
            } catch (IllegalArgumentException e) {
                errors.add(String.format("match[%d].match_id must be a valid UUID", index));
            }
        }
        if (isNullOrBlank(match.getCompetitionId())) {
            errors.add(String.format("match[%d].competition_id is required", index));
        } else {
            try {
                UUID uuid = UUID.fromString(match.getCompetitionId());
            } catch (IllegalArgumentException e) {
                errors.add(String.format("match[%d].competition_id must be a valid UUID", index));
            }
        }
        if (isNullOrBlank(match.getCompetitionName())) {
            errors.add(String.format("match[%d].competition_name is required", index));
        }
        if (isNullOrBlank(match.getMatchName())) {
            errors.add(String.format("match[%d].match_name is required", index));
        }
        if (match.getMatchDate() == null) {
            errors.add(String.format("match[%d].match_date is required", index));
        } else if (match.getMatchDate().isBefore(Instant.now())) {
            errors.add(String.format("match[%d].match_date must be in the future", index));
        }
    }

    private boolean isNullOrBlank(String value) {
        return value == null || value.trim().isEmpty();
    }
}
