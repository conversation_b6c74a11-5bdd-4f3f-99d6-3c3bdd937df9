package com.sportal365.articlescheduler.schedules.application.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sportal365.articlescheduler.domain.model.enums.ScheduleType;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class ScheduleCreateRequest {

    @JsonProperty("template_name")
    private String templateName;

    @JsonProperty("template_type")
    private String templateType;

    @JsonProperty("matches")
    private List<ScheduleMatchRequest> matches;

    @JsonProperty("generate_summary")
    private boolean generateSummary;

    @JsonProperty("generate_strapline")
    private boolean generateStrapline;

    @JsonProperty("category_id")
    private String category;

    @JsonProperty("category_name")
    private String categoryName;

    @JsonProperty("user_id")
    private String userId;

    @JsonProperty("user_name")
    private String userName;

    @JsonProperty("sport")
    private String sport;

    @JsonProperty("schedule_type")
    private ScheduleType scheduleType;
}
