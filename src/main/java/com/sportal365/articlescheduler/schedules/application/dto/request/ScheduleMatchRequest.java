package com.sportal365.articlescheduler.application.dto.schedule.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.time.Instant;
import java.time.LocalDateTime;

@Data
@Builder
public class ScheduleMatchRequest {

    @JsonProperty("match_id")
    private String matchId;

    @JsonProperty("competition_id")
    private String competitionId;

    @JsonProperty("competition_name")
    private String competitionName;

    @JsonProperty("match_name")
    private String matchName;

    @JsonProperty("match_date")
    private Instant matchDate;
}
