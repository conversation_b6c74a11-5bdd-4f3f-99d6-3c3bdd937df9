package com.sportal365.articlescheduler.application.service.schedules.processing;

import com.sportal365.articlescheduler.application.service.article.generation.AsyncArticleGenerationService;
import com.sportal365.articlescheduler.application.service.schedules.SchedulePersistenceService;
import com.sportal365.articlescheduler.domain.model.Schedule;
import com.sportal365.articlescheduler.domain.model.enums.ScheduleStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class ScheduleRetryProcessingService implements InitializingBean {

    private final SchedulePersistenceService schedulePersistenceService;

    private final AsyncArticleGenerationService asyncArticleGenerationService;

    @Override
    public void afterPropertiesSet() {
    }

    @Scheduled(cron = "${retry.cron.expression}")
    public void retryProcessing() {
        List<Schedule> schedules = schedulePersistenceService.findAllByStatus(ScheduleStatus.RETRY);
        if (schedules.isEmpty()) {
            return;
        }

        List<Schedule> schedulesToSave = asyncArticleGenerationService.processSchedules(schedules);

        schedulePersistenceService.saveSchedules(schedulesToSave);
    }
}
