package com.sportal365.articlescheduler.application.service.schedules;

import com.sportal365.articlescheduler.application.dto.schedule.request.ScheduleCreateRequest;
import com.sportal365.articlescheduler.application.dto.schedule.request.ScheduleListRequest;
import com.sportal365.articlescheduler.application.dto.schedule.request.ScheduleMatchRequest;
import com.sportal365.articlescheduler.application.dto.schedule.request.ScheduleUpdateRequest;
import com.sportal365.articlescheduler.application.dto.schedule.response.ScheduleResponse;
import com.sportal365.articlescheduler.application.service.article.generation.AsyncArticleGenerationService;
import com.sportal365.articlescheduler.domain.model.Schedule;
import com.sportal365.articlescheduler.domain.model.enums.ScheduleStatus;
import com.sportal365.articlescheduler.domain.model.enums.ScheduleType;
import com.sportal365.articlescheduler.domain.model.mappers.ScheduleMapper;
import com.sportal365.articlescheduler.domain.query.ScheduleQueryBuilder;
import com.sportal365.articlescheduler.domain.utils.ProjectUtils;
import com.sportal365.common.enums.SportEnum;
import com.sportal365.configurationclient.model.Project;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.time.*;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class ScheduleService {

    private final SchedulePersistenceService schedulePersistenceService;

    private final ScheduleMapper scheduleMapper;

    private final AsyncArticleGenerationService asyncArticleGenerationService;

    private final MongoTemplate mongoTemplate;

    private final ProjectUtils projectUtils;
    private final Executor taskExecutor;


    @Transactional
    public List<ScheduleResponse> createSchedule(ScheduleCreateRequest request, String projectDomain) {
        List<Schedule> schedules = new ArrayList<>();
        Project project = projectUtils.getProject(projectDomain);
        Project.Configuration.Services.AiArticleGenerationService aiArticleGenerationService = project.getConfiguration()
                .getServices().getAiArticleGenerationService();

        // Determine the effective schedule type (use default if not provided)
        ScheduleType effectiveScheduleType = request.getScheduleType() != null ?
            request.getScheduleType() : ScheduleType.getDefault();

        request.getMatches().forEach(match -> {
            String timeZone = project.getConfiguration().getTimezone();
            Schedule.ProviderProperties providerProperties = Schedule.ProviderProperties.builder()
                    .provider(getProvider(aiArticleGenerationService))
                    .llmModel(getModel(aiArticleGenerationService))
                    .build();

            schedules.add(Schedule.builder()
                    .matchDetails(buildMatchDetails(match))
                    .templateName(request.getTemplateName())
                    .generationTime(getScheduleGenerationTime(match))
                    .status(ScheduleStatus.SCHEDULED)
                    .generateStrapline(request.isGenerateStrapline())
                    .generateSummary(request.isGenerateSummary())
                    .projectDomain(projectDomain)
                    .timeZone(timeZone)
                    .category(buildCategory(request))
                    .userId(request.getUserId())
                    .sport(SportEnum.valueOf(request.getSport()))
                    .llmTemperature(aiArticleGenerationService.getTemperature())
                    .providerProperties(providerProperties)
                    .articleLanguage(project.getConfiguration().getLanguages().getDefaultLanguage().getLanguageCode())
                    .userName(request.getUserName())
                    .language(project.getConfiguration().getLanguages().getDefaultLanguage().getLanguageCode())
                    .scheduleType(effectiveScheduleType)
                    .build());
        });

        List<Schedule> savedSchedules = createSchedules(schedules);

        // Check if schedule_type is "immediately" - if so, process ALL schedules immediately
        if (effectiveScheduleType == ScheduleType.IMMEDIATELY) {
            List<Schedule> schedulesForImmediateProcessing = savedSchedules.stream()
                    .peek(schedule -> schedule.setStatus(ScheduleStatus.INPROGRESS))
                    .collect(Collectors.toList());
            updateSchedules(schedulesForImmediateProcessing);
            processImmediateSchedulesAsync(schedulesForImmediateProcessing);
        } else if (effectiveScheduleType == ScheduleType.SCHEDULED) {
            // Use existing scheduling logic for SCHEDULED type
            List<Schedule> schedulesGeneratedToday = filterSchedulesForImmediateProcessing(savedSchedules);
            if (!schedulesGeneratedToday.isEmpty()) {
                processImmediateSchedulesAsync(schedulesGeneratedToday);
            }
        }
        List<ScheduleResponse> scheduleResponses = new ArrayList<>(scheduleMapper.toResponseList(savedSchedules));
        log.debug("Created {} schedules for project: {}", savedSchedules.size(), projectDomain);
        return scheduleResponses;
    }

    private String getProvider(Project.Configuration.Services.AiArticleGenerationService aiArticleGenerationService) {
        if (aiArticleGenerationService.getProviderProperties() != null) {
            return aiArticleGenerationService.getProviderProperties().getProvider();
        }
        return null;
    }

    private String getModel(Project.Configuration.Services.AiArticleGenerationService aiArticleGenerationService) {
        if (aiArticleGenerationService.getProviderProperties() != null) {
            return aiArticleGenerationService.getProviderProperties().getLlmModel();
        }
        return null;
    }

    private static Schedule.Category buildCategory(ScheduleCreateRequest request) {
        return Schedule.Category.builder()
                .id(request.getCategory())
                .name(request.getCategoryName())
                .build();
    }

    private static Schedule.MatchDetails buildMatchDetails(ScheduleMatchRequest match) {

        return Schedule.MatchDetails.builder()
                .matchId(match.getMatchId())
                .competitionId(match.getCompetitionId())
                .competitionName(match.getCompetitionName())
                .matchName(match.getMatchName())
                .matchDate(match.getMatchDate())
                .build();
    }

    private static Instant getScheduleGenerationTime(ScheduleMatchRequest match) {
        LocalDate matchDateUtc = match.getMatchDate().atZone(ZoneId.of("UTC")).toLocalDate();

        LocalDateTime generationTimeUtc = matchDateUtc.atTime(0, 15);

        return generationTimeUtc.atZone(ZoneId.of("UTC")).toInstant();
    }

    public Schedule getSchedule(String id, String projectDomain) {
        return schedulePersistenceService.getScheduleById(id, projectDomain);
    }

    public Page<Schedule> listSchedules(String projectDomain, ScheduleListRequest listRequest) {
        int page = listRequest.getPageRequest().getPage();
        int size = listRequest.getPageRequest().getSize();


        Pageable pageable = PageRequest.of(
                page,
                size,
                Sort.by(Sort.Direction.ASC, "generation_time")
        );
        Query query = ScheduleQueryBuilder.buildQuery(listRequest, pageable, projectDomain);


        Query countQuery = ScheduleQueryBuilder.buildQuery(listRequest, null, projectDomain);
        long total = mongoTemplate.count(countQuery, Schedule.class);

        List<Schedule> schedules = mongoTemplate.find(query, Schedule.class);

        return new PageImpl<>(schedules, pageable, total);
    }

    public Schedule updateSchedule(String id, ScheduleUpdateRequest request, String projectDomain) {
        Schedule schedule = getSchedule(id, projectDomain);

        if (request.getGenerationTime() != null) {
            schedule.setGenerationTime(request.getGenerationTime());
        }

        log.debug("Updating schedule {} for project: {}", id, projectDomain);
        return schedulePersistenceService.saveSchedule(schedule);
    }

    public void deleteSchedule(String id, String projectDomain) {
        Schedule schedule = getSchedule(id, projectDomain);
        schedulePersistenceService.deleteSchedule(schedule);
        log.debug("Deleted schedule {} for project: {}", id, projectDomain);
    }

    public Integer schedulesCount(String projectDomain) {
        return schedulePersistenceService.countActiveSchedules(projectDomain);
    }

    private boolean shouldGenerateImmediately(Schedule schedule) {
        // First check if schedule_type is set to IMMEDIATELY
        if (schedule.getScheduleType() != null && schedule.getScheduleType() == ScheduleType.IMMEDIATELY) {
            return true;
        }

        // Fall back to existing logic for backward compatibility
        return shouldGenerateImmediately(schedule.getMatchDetails().getMatchDate(), schedule.getTimeZone());
    }

    private boolean shouldGenerateImmediately(Instant matchDate, String timeZone) {
        ZonedDateTime matchTime = matchDate
                .atZone(ZoneId.of(timeZone));
        ZonedDateTime now = ZonedDateTime.now(ZoneId.of(timeZone));

        return matchTime.toLocalDate().equals(now.toLocalDate()) && matchTime.isAfter(now);
    }

    /**
     * Filters the given list of schedules for those that should be processed immediately.
     * It also updates their status to INPROGRESS.
     *
     * @param schedules the list of schedules to filter
     * @return a list of schedules that should be processed immediately
     */
    @Transactional
    public List<Schedule> filterSchedulesForImmediateProcessing(List<Schedule> schedules) {
        schedules = schedules.stream()
                .filter(schedule -> shouldGenerateImmediately(schedule.getMatchDetails().getMatchDate(), schedule.getTimeZone()))
                .peek(schedule -> schedule.setStatus(ScheduleStatus.INPROGRESS))
                .collect(Collectors.toList());
        return updateSchedules(schedules);
    }

    /**
     * Asynchronously processes a list of schedules that are scheduled for immediate article generation.
     *
     * <p>This method initiates an asynchronous task using the configured task executor. It performs the following steps:
     * <ol>
     *     <li>Submits an asynchronous task that calls {@code asyncArticleGenerationService.generateArticlesAsync()}
     *         to start generating articles for each schedule in the provided list.</li>
     *     <li>Waits for all asynchronous article generation tasks to complete using
     *         {@code CompletableFuture.allOf(...).join()}.</li>
     *     <li>Collects the updated schedules after processing by joining each individual future.</li>
     *     <li>Persists the updated schedules using the check-and-save logic in
     *         {@code scheduleCreationService.createSchedulesIfAbsent()}, ensuring that any updates (such as status changes)
     *         are saved to the database.</li>
     *     <li>Logs an informational message upon successful completion of all processing tasks.</li>
     *     <li>Catches and logs any exceptions that occur during the asynchronous processing.</li>
     * </ol>
     * </p>
     *
     * @param schedules the list of schedules to be processed immediately
     */
    @Transactional
    public void processImmediateSchedulesAsync(List<Schedule> schedules) {
        CompletableFuture.runAsync(() -> {
            try {
                List<CompletableFuture<Schedule>> futures = asyncArticleGenerationService.generateArticlesAsync(schedules);
                // Wait for all futures to complete
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

                // Collect the updated schedules after async processing
                List<Schedule> updatedSchedules = futures.stream()
                        .map(CompletableFuture::join)
                        .collect(Collectors.toList());

                // Save updated schedules (using the same check-and-save logic)
                updateSchedules(updatedSchedules);
                log.info("Completed async processing of {} schedules for immediate generation", schedules.size());
            } catch (Exception e) {
                log.error("Error during async schedule processing: {}", e.getMessage(), e);
            }
        }, taskExecutor);
        log.debug("Started async processing for {} schedules requiring immediate generation", schedules.size());
    }

    /**
     * Creates and persists a list of schedules only if they do not already exist for the specified project domain.
     *
     * <p>This method operates within a transactional context to ensure that the check-and-save operations
     * are performed atomically. For each schedule in the provided list, it retrieves the match ID and checks
     * whether a schedule with that match ID already exists for the given project domain using
     * {@code schedulePersistenceService.isScheduleAbsent()}. If the schedule is absent, it attempts to save the schedule
     * using {@code schedulePersistenceService.saveSchedule()}. In cases where a concurrent insertion results in a
     * {@code DataIntegrityViolationException}, the exception is caught and an informational log entry is recorded,
     * while the method continues processing the remaining schedules.</p>
     *
     * @param schedules     the list of schedule entities to be created
     * @return a list of schedules that were successfully created and persisted
     */
    @Transactional
    public List<Schedule> createSchedules(List<Schedule> schedules) {
        List<Schedule> createdSchedules = new ArrayList<>();
        for (Schedule schedule : schedules) {
                try {
                    UUID scheduleId = UUID.randomUUID();
                    schedule.setId(scheduleId.toString());
                    Schedule savedSchedule = schedulePersistenceService.saveSchedule(schedule);
                    createdSchedules.add(savedSchedule);
                } catch (DataIntegrityViolationException e) {
                    log.info("Schedule {} already exists", schedule.getMatchDetails().getMatchId());
                }
        }
        return createdSchedules;
    }

    @Transactional
    public List<Schedule> updateSchedules(List<Schedule> schedules) {
        return schedulePersistenceService.saveSchedules(schedules);
    }
}
