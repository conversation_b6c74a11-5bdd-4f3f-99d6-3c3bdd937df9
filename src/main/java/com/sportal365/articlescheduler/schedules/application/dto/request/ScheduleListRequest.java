package com.sportal365.articlescheduler.application.dto.schedule.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sportal365.articlescheduler.application.dto.common.PageRequest;
import lombok.Builder;
import lombok.Value;

import java.time.Instant;

@Value
@Builder
public class ScheduleListRequest {

    @JsonProperty("category_name")
    String categoryName;

    @JsonProperty("event_name")
    String eventName;

    @JsonProperty("competition_name")
    String competitionName;

    @JsonProperty("from_date")
    Instant from;

    @JsonProperty("to_date")
    Instant to;

    @JsonProperty("created_by")
    String createdBy;

    @JsonProperty("time_zone")
    String timeZone;

    @JsonProperty("page_request")
    PageRequest pageRequest;

    @JsonProperty("status")
    String status;
}
