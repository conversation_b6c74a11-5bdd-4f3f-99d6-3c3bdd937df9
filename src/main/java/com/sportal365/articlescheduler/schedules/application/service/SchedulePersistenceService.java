package com.sportal365.articlescheduler.schedules.application.service;

import com.sportal365.articlescheduler.domain.exception.NotFoundException;
import com.sportal365.articlescheduler.schedules.domain.model.Schedule;
import com.sportal365.articlescheduler.schedules.domain.model.enums.ScheduleStatus;
import com.sportal365.articlescheduler.schedules.domain.repository.ScheduleRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class SchedulePersistenceService {

    private final ScheduleRepository scheduleRepository;

    /**
     * Saves a list of Schedule entities to the database.
     *
     * <p>This method delegates the saving of schedules to the underlying repository's {@code saveAll} method.
     * It returns the list of saved schedules, which may include any updates (such as generated IDs) made during the persistence process.
     * This abstraction centralizes all persistence-related operations for Schedule entities,
     * ensuring that any additional logic or validation related to saving schedules can be managed in one place.</p>
     *
     * @param schedules the list of Schedule entities to be saved
     * @return the list of Schedule entities after being saved to the database
     */
    public List<Schedule> saveSchedules(List<Schedule> schedules) {
        return scheduleRepository.saveAll(schedules);
    }

    /**
     * Finds all schedules with the specified status.
     *
     * @param status the status to filter schedules (e.g., ScheduleStatus.RETRY)
     * @return a list of schedules matching the provided status
     */
    public List<Schedule> findAllByStatus(ScheduleStatus status) {
        return scheduleRepository.findAllByStatus(status);
    }

    /**
     * Retrieves a Schedule by its unique identifier and project domain.
     *
     * <p>This method attempts to find a Schedule entity in the repository using the provided ID and Project domain.
     * If no Schedule is found, it throws a {@code NotFoundException} with an appropriate message.</p>
     *
     * @param id the unique identifier of the Schedule to retrieve.
     * @param projectDomain the project domain associated with the schedule.
     * @return the Schedule entity corresponding to the provided ID and Project domain.
     * @throws NotFoundException if no Schedule is found for the given ID and Project domain.
     */
    public Schedule getScheduleById(String id, String projectDomain) {
        return scheduleRepository.findByIdAndProjectDomain(id, projectDomain)
                .orElseThrow(() -> new NotFoundException("Schedule not found"));
    }

    /**
     * Saves a Schedule entity to the repository.
     *
     * <p>This method delegates the saving operation to the underlying repository's {@code save} method.
     * It returns the saved Schedule entity, which may include updates such as generated IDs or timestamps
     * that were applied during the persistence process.</p>
     *
     * @param schedule the Schedule entity to be saved
     * @return the persisted Schedule entity with any updates applied during the save operation
     */
    public Schedule saveSchedule(Schedule schedule) {
        return scheduleRepository.save(schedule);
    }

    /**
     * Deletes the specified Schedule entity from the repository.
     *
     * <p>This method delegates the deletion operation to the underlying repository's {@code delete} method.
     * It removes the provided Schedule entity from the database.</p>
     *
     * @param schedule the Schedule entity to be deleted
     */
    public void deleteSchedule(Schedule schedule) {
        scheduleRepository.delete(schedule);
    }

    /**
     * Counts the number of active schedules for a specific project domain.
     *
     * <p>This method queries the repository to count schedules that have a status of either
     * SCHEDULED, RETRY, or INPROGRESS for the given project domain. This can be useful for
     * monitoring the current workload or implementing throttling mechanisms based on the
     * number of active schedules.</p>
     *
     * @param projectDomain the project domain to count active schedules for
     * @return the number of active schedules (those with status SCHEDULED, RETRY, or INPROGRESS)
     */
    public Integer countActiveSchedules(String projectDomain) {
        return scheduleRepository.countByStatusInAndProjectDomain(List.of(ScheduleStatus.SCHEDULED,
                ScheduleStatus.RETRY, ScheduleStatus.INPROGRESS), projectDomain);
    }
}
