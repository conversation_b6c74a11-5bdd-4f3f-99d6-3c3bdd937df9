package com.sportal365.articlescheduler.application.dto.schedule.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.time.Instant;
import java.time.LocalDateTime;

@Data
@Builder
public class ScheduleUpdateRequest {

    @JsonProperty("bookmaker_id")
    private String bookmakerId;

    @JsonProperty("generation_time")
    private Instant generationTime;
}
