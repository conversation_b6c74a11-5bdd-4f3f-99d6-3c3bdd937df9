#!/bin/bash

echo "Fixing remaining import issues..."

# Update imports for TemplateId
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.infrastructure\.persistence\.entity\.TemplateId;|import com.sportal365.articlescheduler.article.generation.infrastructure.persistence.entity.TemplateId;|g' {} \;

# Update imports for contentapi model classes that are now in the client package
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.infrastructure\.client\.contentapi\.model\.|import com.sportal365.articlescheduler.article.generation.infrastructure.client.model.|g' {} \;

# Update imports for llm configuration classes
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.infrastructure\.client\.llm\.configuration\.|import com.sportal365.articlescheduler.article.generation.infrastructure.client.configuration.|g' {} \;

# Update imports for contentapi configuration classes
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.infrastructure\.client\.contentapi\.configuration\.|import com.sportal365.articlescheduler.article.generation.infrastructure.client.configuration.|g' {} \;

# Update imports for contentapi constants
find src/main/java -name "*.java" -type f -exec sed -i '' 's|import com\.sportal365\.articlescheduler\.infrastructure\.client\.contentapi\.constants\.|import com.sportal365.articlescheduler.article.generation.infrastructure.client.constants.|g' {} \;

echo "Remaining import issues fixed!"
